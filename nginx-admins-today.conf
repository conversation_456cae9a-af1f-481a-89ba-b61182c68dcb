# nginx конфигурация для admins.today
# Поместить в /etc/nginx/sites-available/admins.today

server {
    listen 80;
    server_name admins.today www.admins.today;
    
    # Редирект на HTTPS (в продакшене)
    # return 301 https://$server_name$request_uri;
    
    root /var/www/admins.today;
    index index.html;
    
    # Логи
    access_log /var/log/nginx/admins.today.access.log;
    error_log /var/log/nginx/admins.today.error.log;
    
    # ===== БЕЗОПАСНОСТЬ =====
    
    # Запрещаем доступ к секретным файлам
    location ~ ^/api/(config\.php|\.env)$ {
        deny all;
        return 404;
    }
    
    # Запрещаем доступ к папке с заказами
    location ~ ^/api/orders/ {
        deny all;
        return 404;
    }
    
    # Запрещаем доступ к системным файлам
    location ~ /\.(git|htaccess|htpasswd) {
        deny all;
        return 404;
    }
    
    # Запрещаем доступ к backup файлам
    location ~ \.(bak|backup|old|tmp|log|json)$ {
        deny all;
        return 404;
    }
    
    # ===== API ENDPOINTS =====
    
    # Rate limiting для API
    limit_req_zone $binary_remote_addr zone=api:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=callback:10m rate=10r/m;
    
    # API для создания платежа
    location = /api/payment-api.php {
        limit_req zone=api burst=10 nodelay;
        
        fastcgi_pass unix:/var/run/php/php8.1-fmp.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type' always;
        
        # Preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # Callback от WayForPay
    location = /api/payment-callback.php {
        limit_req zone=callback burst=20 nodelay;
        
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Только POST запросы
        if ($request_method !~ ^(POST)$) {
            return 405;
        }
    }
    
    # Запрещаем выполнение других PHP файлов в api/
    location ~ ^/api/.*\.php$ {
        return 404;
    }
    
    # ===== ЧИСТЫЕ URL =====
    
    # Главная страница
    location = / {
        try_files /index.html =404;
    }
    
    # Страницы услуг
    location ~ ^/(techsupport|sysadmin|cloud|security)$ {
        try_files /$1.html =404;
    }
    
    # Правовые страницы
    location ~ ^/(contacts|refund-policy|terms-of-service|privacy-policy)$ {
        try_files /$1.html =404;
    }

    # Страницы оплаты
    location ~ ^/(payment-success|payment-error)$ {
        try_files /$1.html =404;
    }
    
    # Статические файлы
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # HTML файлы (если обращаются напрямую)
    location ~ \.html$ {
        expires 1h;
        add_header Cache-Control "public";
        try_files $uri =404;
    }
    
    # ===== БЕЗОПАСНОСТЬ HEADERS =====
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # CSP для дополнительной безопасности
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://secure.wayforpay.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.telegram.org https://secure.wayforpay.com;" always;
    
    # ===== FALLBACK =====
    
    # Все остальное - 404
    location / {
        try_files $uri $uri/ =404;
    }
}

# HTTPS версия (для продакшена)
# server {
#     listen 443 ssl http2;
#     server_name admins.today www.admins.today;
#     
#     ssl_certificate /path/to/ssl/cert.pem;
#     ssl_certificate_key /path/to/ssl/private.key;
#     
#     # Остальная конфигурация такая же...
# }
