# Nginx site configuration for Admins.Today
# Place this file in /etc/nginx/sites-available/admins.today
# Then create symlink: ln -s /etc/nginx/sites-available/admins.today /etc/nginx/sites-enabled/

server {
    listen 80;
    server_name admins.today www.admins.today;
    
    root /var/www/admins.today;
    index index.html;
    charset utf-8;
    
    # ===== CLEAN URL RULES =====
    
    # Remove .html extension (301 redirect)
    location ~ ^(.+)\.html$ {
        return 301 $1;
    }
    
    # Main routes
    location = / { try_files /index.html =404; }
    location = /index { try_files /index.html =404; }
    
    # Service pages
    location = /techsupport { try_files /techsupport.html =404; }
    location = /sysadmin { try_files /sysadmin.html =404; }
    location = /cloud { try_files /cloud.html =404; }
    location = /security { try_files /security.html =404; }
    location = /contacts { try_files /contacts.html =404; }
    
    # Legal pages
    location = /refund-policy { try_files /refund-policy.html =404; }
    location = /terms-of-service { try_files /terms-of-service.html =404; }
    location = /privacy-policy { try_files /privacy-policy.html =404; }
    
    # Generic rule for other pages
    location ~ ^/([^./]+)/?$ {
        try_files /$1.html =404;
    }
    
    # ===== STATIC FILES =====
    
    location ~* \.(css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # ===== SECURITY =====
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # ===== ERROR PAGES =====
    
    error_page 404 /404.html;
    error_page 500 502 503 504 /500.html;
    
    # ===== DENY ACCESS =====
    
    location ~ /\. { deny all; }
    location ~ ~$ { deny all; }
    location ~ \.php$ { return 404; }
    
    # ===== REMOVE TRAILING SLASH =====
    
    location ~ ^(.+)/$ {
        return 301 $1;
    }
}
