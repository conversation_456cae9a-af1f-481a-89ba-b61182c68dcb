<?php
// payment-api.php - API для создания платежа
// Этот файл ДОСТУПЕН извне, но без секретов!

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    exit(0);
}

// Загружаем конфиг
$config = require_once __DIR__ . '/config.php';

// Проверяем origin (в продакшене)
$origin = $_SERVER['HTTP_ORIGIN'] ?? $_SERVER['HTTP_REFERER'] ?? '';
$allowed = false;
foreach ($config['security']['allowed_origins'] as $allowedOrigin) {
    if (strpos($origin, $allowedOrigin) === 0) {
        $allowed = true;
        header("Access-Control-Allow-Origin: $allowedOrigin");
        break;
    }
}

if (!$allowed && !empty($origin)) {
    header('Access-Control-Allow-Origin: *'); // Для разработки
}

// Проверяем метод
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    exit(json_encode(['error' => 'Method not allowed']));
}

// Простая защита от спама
session_start();
$now = time();
$requests = $_SESSION['payment_requests'] ?? [];
$requests = array_filter($requests, function($time) use ($now) {
    return $now - $time < 60; // последняя минута
});

if (count($requests) >= $config['security']['rate_limit']['requests_per_minute']) {
    http_response_code(429);
    exit(json_encode(['error' => 'Too many requests']));
}

$requests[] = $now;
$_SESSION['payment_requests'] = $requests;

// Получаем данные заказа
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    exit(json_encode(['error' => 'Invalid JSON data']));
}

// Валидация обязательных полей
$required = ['name', 'email', 'tariff', 'price'];
foreach ($required as $field) {
    if (empty($input[$field])) {
        http_response_code(400);
        exit(json_encode(['error' => "Missing required field: $field"]));
    }
}

// Валидация email
if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    exit(json_encode(['error' => 'Invalid email format']));
}

// Валидация цены
$price = floatval($input['price']);
if ($price <= 0 || $price > 1000) {
    http_response_code(400);
    exit(json_encode(['error' => 'Invalid price range']));
}

// Создаем уникальный номер заказа
$orderReference = 'admins_' . date('Ymd') . '_' . time() . '_' . rand(1000, 9999);
$orderDate = time();

// Подготавливаем данные для WayForPay
$paymentData = [
    'merchantAccount' => $config['wayforpay']['merchant_account'],
    'merchantDomainName' => $config['wayforpay']['domain'],
    'orderReference' => $orderReference,
    'orderDate' => $orderDate,
    'amount' => $price,
    'currency' => 'USD',
    'productName' => [$input['tariffName'] ?? 'Admins.Today Service'],
    'productPrice' => [$price],
    'productCount' => [1],
    'clientFirstName' => trim(explode(' ', $input['name'])[0]),
    'clientLastName' => trim(explode(' ', $input['name'], 2)[1] ?? ''),
    'clientEmail' => $input['email'],
    'clientPhone' => '',
    'language' => 'UA',
    'returnUrl' => 'https://admins.today/payment-success',
    'serviceUrl' => 'https://admins.today/api/payment-callback.php'
];

// Генерируем подпись (БЕЗОПАСНО - на сервере!)
$signatureString = implode(';', [
    $paymentData['merchantAccount'],
    $paymentData['merchantDomainName'],
    $paymentData['orderReference'],
    $paymentData['orderDate'],
    $paymentData['amount'],
    $paymentData['currency'],
    $paymentData['productName'][0],
    $paymentData['productCount'][0],
    $paymentData['productPrice'][0]
]);

$paymentData['merchantSignature'] = hash_hmac('md5', $signatureString, $config['wayforpay']['secret_key']);

// Сохраняем данные заказа для отслеживания
$orderData = [
    'order_reference' => $orderReference,
    'client_data' => [
        'name' => $input['name'],
        'email' => $input['email'],

    ],
    'service_data' => [
        'tariff' => $input['tariff'],
        'tariff_name' => $input['tariffName'] ?? '',
        'price' => $price,
        'service' => $input['service'] ?? '',
        'urgency' => $input['urgency'] ?? '',
        'description' => $input['description'] ?? ''
    ],
    'payment_data' => [
        'amount' => $price,
        'currency' => 'USD',
        'status' => 'pending'
    ],
    'timestamps' => [
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ],
    'meta' => [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]
];

// Создаем папку для заказов если не существует
$ordersDir = __DIR__ . '/orders';
if (!is_dir($ordersDir)) {
    mkdir($ordersDir, 0700, true);
}

// Сохраняем заказ
$orderFile = $ordersDir . '/' . $orderReference . '.json';
if (file_put_contents($orderFile, json_encode($orderData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
    // Логируем создание заказа
    error_log("Order created: $orderReference for {$input['email']}");
} else {
    error_log("Failed to save order: $orderReference");
}

// Возвращаем данные для WayForPay (БЕЗ секретного ключа!)
echo json_encode($paymentData, JSON_UNESCAPED_UNICODE);
?>
