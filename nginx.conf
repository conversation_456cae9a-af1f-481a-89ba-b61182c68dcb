# Nginx configuration for Admins.Today
# Конфигурация для чистых URL без расширения .html

server {
    listen 80;
    listen [::]:80;
    server_name admins.today www.admins.today;
    
    # Redirect HTTP to HTTPS (uncomment when SSL is configured)
    # return 301 https://$server_name$request_uri;
    
    root /var/www/admins.today;
    index index.html;
    
    # Charset
    charset utf-8;
    
    # ===== CLEAN URL CONFIGURATION =====
    
    # Remove .html extension from URLs (301 redirect)
    location ~ ^(.+)\.html$ {
        return 301 $1;
    }
    
    # Main page
    location = / {
        try_files /index.html =404;
    }
    
    # Handle /index specifically
    location = /index {
        try_files /index.html =404;
    }
    
    # Service pages
    location = /techsupport {
        try_files /techsupport.html =404;
    }
    
    location = /sysadmin {
        try_files /sysadmin.html =404;
    }
    
    location = /cloud {
        try_files /cloud.html =404;
    }
    
    location = /security {
        try_files /security.html =404;
    }
    
    # Contact page
    location = /contacts {
        try_files /contacts.html =404;
    }
    
    # Legal pages
    location = /refund-policy {
        try_files /refund-policy.html =404;
    }
    
    location = /terms-of-service {
        try_files /terms-of-service.html =404;
    }
    
    location = /privacy-policy {
        try_files /privacy-policy.html =404;
    }
    
    # Test page
    location = /test-url {
        try_files /test-url.html =404;
    }
    
    # Generic rule for any other pages without extension
    location ~ ^/([^./]+)/?$ {
        try_files /$1.html =404;
    }
    
    # ===== STATIC FILES OPTIMIZATION =====
    
    # CSS and JS files
    location ~* \.(css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        gzip_static on;
    }
    
    # Images
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # Fonts
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # ===== SECURITY HEADERS =====
    
    # Security headers for all responses
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Download-Options "noopen" always;
    add_header X-Permitted-Cross-Domain-Policies "none" always;
    
    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://secure.wayforpay.com https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https://api.telegram.org https://secure.wayforpay.com;" always;
    
    # ===== GZIP COMPRESSION =====
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # ===== ERROR PAGES =====
    
    error_page 404 /404.html;
    error_page 500 502 503 504 /500.html;
    
    location = /404.html {
        internal;
    }
    
    location = /500.html {
        internal;
    }
    
    # ===== SECURITY RESTRICTIONS =====
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to configuration files
    location ~* \.(conf|config|sql|log|bak|backup|old|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # ===== LOGGING =====
    
    access_log /var/log/nginx/admins.today.access.log;
    error_log /var/log/nginx/admins.today.error.log;
    
    # ===== ADDITIONAL OPTIMIZATIONS =====
    
    # Remove trailing slashes
    location ~ ^(.+)/$ {
        return 301 $1;
    }
    
    # Prevent access to PHP files (if any)
    location ~ \.php$ {
        return 404;
    }
    
    # Handle favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # Handle robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
}

# HTTPS configuration (uncomment and configure when SSL is ready)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name admins.today www.admins.today;
#     
#     root /var/www/admins.today;
#     index index.html;
#     
#     # SSL Configuration
#     ssl_certificate /path/to/ssl/certificate.crt;
#     ssl_certificate_key /path/to/ssl/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # Include all the same location blocks from HTTP configuration above
#     # ... (copy all location blocks from the HTTP server block)
# }
