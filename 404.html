<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Страница не найдена | Admins.Today</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚙️</text></svg>">
</head>
<body>
    <div class="error-page">
        <div class="container">
            <div class="error-content">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1 class="error-title">404</h1>
                <h2 class="error-subtitle">Страница не найдена</h2>
                <p class="error-description">
                    К сожалению, запрашиваемая страница не существует или была перемещена.
                    Наши системные администраторы уже работают над решением проблемы.
                </p>
                <div class="error-actions">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        На главную
                    </a>
                    <a href="/#contact" class="btn btn-secondary">
                        <i class="fas fa-envelope"></i>
                        Связаться с нами
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-bg);
            padding: 20px;
        }

        .error-content {
            text-align: center;
            max-width: 600px;
        }

        .error-icon {
            font-size: 4rem;
            color: var(--warning-color);
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 6rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .error-subtitle {
            font-size: 2rem;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .error-description {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .error-title {
                font-size: 4rem;
            }
            
            .error-subtitle {
                font-size: 1.5rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</body>
</html>
