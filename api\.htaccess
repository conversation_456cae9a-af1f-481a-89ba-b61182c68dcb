# .htaccess для папки api/ - БЕЗОПАСНОСТЬ!

# Запрещаем доступ к секретным файлам
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# Запрещаем доступ к папке с заказами
<Directory "orders">
    Order allow,deny
    Deny from all
</Directory>

# Запрещаем доступ к backup и log файлам
<FilesMatch "\.(bak|backup|old|tmp|log|json)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Разрешаем только определенные PHP файлы
<FilesMatch "^(payment-api|payment-callback)\.php$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Запрещаем выполнение других PHP файлов
<FilesMatch "\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Защита от просмотра директории
Options -Indexes

# Дополнительная безопасность
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Rate limiting (если поддерживается)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        5
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
