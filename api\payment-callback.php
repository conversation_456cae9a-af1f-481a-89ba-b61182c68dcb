<?php
// payment-callback.php - Обработка результата от WayForPay
// Этот файл вызывается WayForPay после оплаты

header('Content-Type: text/plain; charset=utf-8');

// Загружаем конфиг
$config = require_once __DIR__ . '/config.php';

// Логируем входящий запрос
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
];

// Получаем данные от WayForPay
$input = null;
$rawInput = file_get_contents('php://input');

if (!empty($rawInput)) {
    $input = json_decode($rawInput, true);
    $logData['raw_input'] = $rawInput;
}

if (!$input && !empty($_POST)) {
    $input = $_POST;
    $logData['post_data'] = $_POST;
}

if (!$input) {
    $logData['error'] = 'No input data received';
    error_log('WayForPay callback error: ' . json_encode($logData));
    http_response_code(400);
    exit('No data received');
}

$logData['parsed_input'] = $input;

// Проверяем обязательные поля
$required = ['merchantAccount', 'orderReference', 'merchantSignature', 'transactionStatus'];
foreach ($required as $field) {
    if (empty($input[$field])) {
        $logData['error'] = "Missing field: $field";
        error_log('WayForPay callback error: ' . json_encode($logData));
        http_response_code(400);
        exit("Missing field: $field");
    }
}

// Проверяем merchant account
if ($input['merchantAccount'] !== $config['wayforpay']['merchant_account']) {
    $logData['error'] = 'Invalid merchant account';
    error_log('WayForPay callback error: ' . json_encode($logData));
    http_response_code(400);
    exit('Invalid merchant account');
}

// Генерируем ожидаемую подпись для проверки
$signatureFields = [
    'merchantAccount',
    'orderReference', 
    'amount',
    'currency'
];

// Добавляем дополнительные поля если они есть
if (!empty($input['authCode'])) {
    $signatureFields[] = 'authCode';
}
if (!empty($input['cardPan'])) {
    $signatureFields[] = 'cardPan';
}
if (!empty($input['transactionStatus'])) {
    $signatureFields[] = 'transactionStatus';
}
if (!empty($input['reasonCode'])) {
    $signatureFields[] = 'reasonCode';
}

$signatureString = '';
foreach ($signatureFields as $field) {
    $signatureString .= ($input[$field] ?? '') . ';';
}
$signatureString = rtrim($signatureString, ';');

$expectedSignature = hash_hmac('md5', $signatureString, $config['wayforpay']['secret_key']);

// Проверяем подпись
if ($input['merchantSignature'] !== $expectedSignature) {
    $logData['error'] = 'Invalid signature';
    $logData['expected_signature'] = $expectedSignature;
    $logData['received_signature'] = $input['merchantSignature'];
    $logData['signature_string'] = $signatureString;
    error_log('WayForPay callback error: ' . json_encode($logData));
    http_response_code(400);
    exit('Invalid signature');
}

// Загружаем данные заказа
$orderFile = __DIR__ . '/orders/' . $input['orderReference'] . '.json';
if (!file_exists($orderFile)) {
    $logData['error'] = 'Order not found';
    error_log('WayForPay callback error: ' . json_encode($logData));
    http_response_code(404);
    exit('Order not found');
}

$orderData = json_decode(file_get_contents($orderFile), true);
if (!$orderData) {
    $logData['error'] = 'Invalid order data';
    error_log('WayForPay callback error: ' . json_encode($logData));
    http_response_code(500);
    exit('Invalid order data');
}

// Обновляем данные заказа
$orderData['payment_data']['status'] = $input['transactionStatus'];
$orderData['payment_data']['transaction_id'] = $input['authCode'] ?? '';
$orderData['payment_data']['card_pan'] = $input['cardPan'] ?? '';
$orderData['payment_data']['reason_code'] = $input['reasonCode'] ?? '';
$orderData['payment_data']['callback_data'] = $input;
$orderData['timestamps']['payment_updated_at'] = date('Y-m-d H:i:s');

// Сохраняем обновленные данные
file_put_contents($orderFile, json_encode($orderData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

$logData['order_updated'] = true;
$logData['transaction_status'] = $input['transactionStatus'];

// Если оплата успешна - отправляем в Telegram
if ($input['transactionStatus'] === 'Approved') {
    $telegramResult = sendToTelegram($orderData, $config);
    $logData['telegram_sent'] = $telegramResult;
    
    error_log('Payment approved: ' . json_encode($logData));
} else {
    error_log('Payment failed: ' . json_encode($logData));
}

// Отвечаем WayForPay
echo 'OK';

function sendToTelegram($orderData, $config) {
    $client = $orderData['client_data'];
    $service = $orderData['service_data'];
    $payment = $orderData['payment_data'];
    
    $message = "💳 <b>ОПЛАЧЕНО! Нова заявка Admins.Today</b>\n\n";
    $message .= "👤 <b>Клієнт:</b> " . htmlspecialchars($client['name']) . "\n";
    $message .= "📧 <b>Email:</b> " . htmlspecialchars($client['email']) . "\n";
    

    
    $message .= "💰 <b>Сума:</b> $" . $payment['amount'] . " " . $payment['currency'] . "\n";
    $message .= "📦 <b>Тариф:</b> " . htmlspecialchars($service['tariff_name']) . "\n";
    
    if (!empty($payment['transaction_id'])) {
        $message .= "🆔 <b>ID транзакції:</b> " . htmlspecialchars($payment['transaction_id']) . "\n";
    }
    
    if (!empty($service['description'])) {
        $message .= "\n📝 <b>Опис:</b>\n" . htmlspecialchars($service['description']) . "\n";
    }
    
    $message .= "\n⏰ <b>Час:</b> " . $orderData['timestamps']['created_at'];
    $message .= "\n🌐 <b>Джерело:</b> admins.today";
    $message .= "\n\n#оплачено #" . $service['tariff'];
    
    $url = "https://api.telegram.org/bot" . $config['telegram']['bot_token'] . "/sendMessage";
    $data = [
        'chat_id' => $config['telegram']['chat_id'],
        'text' => $message,
        'parse_mode' => 'HTML'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'success' => $httpCode === 200,
        'http_code' => $httpCode,
        'response' => $result
    ];
}
?>
