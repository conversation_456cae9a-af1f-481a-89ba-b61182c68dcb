// <PERSON>ript to fix navigation and links in all HTML files
// Run with: node fix-navigation.js

const fs = require('fs');
const path = require('path');

const files = [
    'sysadmin.html',
    'cloud.html', 
    'security.html',
    'contacts.html',
    'refund-policy.html',
    'terms-of-service.html',
    'privacy-policy.html'
];

const correctNavigation = `    <nav class="navbar">
        <div class="container">
            <div class="nav-logo">
                <div class="nav-futuristic-icon">
                    <div class="nav-icon-core">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="nav-icon-rings">
                        <div class="nav-ring nav-ring-1"></div>
                        <div class="nav-ring nav-ring-2"></div>
                    </div>
                </div>
                <span>Admins.Today</span>
            </div>
            <div class="nav-links">
                <a href="/">Головна</a>
                <a href="/#services">Послуги</a>
                <a href="/#order">Замовити</a>
                <a href="/contacts">Контакти</a>
            </div>
        </div>
    </nav>`;

const correctFooter = `    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="footer-futuristic-icon">
                            <div class="footer-icon-core">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="footer-icon-rings">
                                <div class="footer-ring footer-ring-1"></div>
                                <div class="footer-ring footer-ring-2"></div>
                            </div>
                        </div>
                        <span>Admins.Today</span>
                    </div>
                    <p>Професійні системні адміністратори та техпідтримка для вашого бізнесу</p>
                </div>
                <div class="footer-section">
                    <h4>Послуги</h4>
                    <ul>
                        <li><a href="/techsupport">Техпідтримка 24/7</a></li>
                        <li><a href="/sysadmin">Системне адміністрування</a></li>
                        <li><a href="/cloud">Хмарні рішення</a></li>
                        <li><a href="/security">Безпека</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Правова інформація</h4>
                    <ul>
                        <li><a href="/refund-policy">Правила повернення коштів</a></li>
                        <li><a href="/terms-of-service">Правила та умови надання послуг</a></li>
                        <li><a href="/privacy-policy">Політика конфіденційності</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Контакти</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-clock"></i> 24/7</p>
                        <p><a href="/contacts" style="color: var(--primary-color);">Детальна контактна інформація</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Admins.Today. Усі права захищені.</p>
            </div>
        </div>
    </footer>`;

files.forEach(filename => {
    if (fs.existsSync(filename)) {
        let content = fs.readFileSync(filename, 'utf8');
        
        // Fix navigation
        content = content.replace(/<nav class="navbar">[\s\S]*?<\/nav>/g, correctNavigation);
        
        // Fix footer
        content = content.replace(/<footer class="footer">[\s\S]*?<\/footer>/g, correctFooter);
        
        // Fix links
        content = content.replace(/href="index\.html"/g, 'href="/"');
        content = content.replace(/href="index\.html#/g, 'href="/#');
        content = content.replace(/href="([^"]+)\.html"/g, 'href="/$1"');
        
        fs.writeFileSync(filename, content);
        console.log(`✅ Fixed ${filename}`);
    } else {
        console.log(`❌ File not found: ${filename}`);
    }
});

console.log('🎉 All files fixed!');
